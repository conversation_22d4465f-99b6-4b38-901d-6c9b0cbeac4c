global class DatacloudInternalCompany {
	global String domesticUltimateDuns;
	global ConnectApi.DatacloudCompany externalRepresentation;
	global String globalUltimateDuns;
	global Boolean isInCrm;
	global Boolean isMarketable;
	global String parentDuns;
	global DatacloudInternalCompany() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}