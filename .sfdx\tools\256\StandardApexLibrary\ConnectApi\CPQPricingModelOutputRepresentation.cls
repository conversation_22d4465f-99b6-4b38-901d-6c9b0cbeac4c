global class CPQPricingModelOutputRepresentation {
	global String frequency;
	global String id;
	global String name;
	global Integer occurrence;
	global String pricingModelType;
	global String unitOfMeasure;
	global CPQPricingModelOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}