global class DatacloudContact {
	global ConnectApi.Address address;
	global String companyId;
	global String companyName;
	global String contactId;
	global String department;
	global String email;
	global String firstName;
	global Boolean isInactive;
	global Boolean isOwned;
	global String lastName;
	global String level;
	global List<ConnectApi.PhoneNumber> phoneNumbers;
	global String title;
	global Datetime updatedDate;
	global DatacloudContact() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}