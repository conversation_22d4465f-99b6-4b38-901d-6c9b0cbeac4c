global class ContractDocumentVersionResponse {
	global String contractDocumentVersionId;
	global String contractId;
	global String createdByFullName;
	global String createdDate;
	global String creationProcessType;
	global String documentTemplateId;
	global Boolean isDocumentVersionLocked;
	global String lastModifiedDate;
	global String lockType;
	global String lockedByUserName;
	global String name;
	global String status;
	global String versionNumber;
	global ContractDocumentVersionResponse() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}