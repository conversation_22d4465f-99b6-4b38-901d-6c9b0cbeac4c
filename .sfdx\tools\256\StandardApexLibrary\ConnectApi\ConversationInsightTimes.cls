global class ConversationInsightTimes {
	global String action;
	global Double endOffset;
	global Boolean isFollowup;
	global Long mentionId;
	global Double momentOffset;
	global Boolean needsAttention;
	global List<Integer> sequences;
	global String speakerId;
	global String speakerRole;
	global Double startOffset;
	global ConversationInsightTimes() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}