global class DataKitDeployComponentConfigCalculatedInsightInput {
	global String apiName;
	global String apiNameOverride;
	global String label;
	global ConnectApi.CalculatedInsightPublishScheduleInterval publishInterval;
	global DataKitDeployComponentConfigCalculatedInsightInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}