global class CPQProductSellingModelOptionOutputRepresentation {
	global String id;
	global String productId;
	global ConnectApi.CPQProductSellingModelOutputRepresentation productSellingModel;
	global String productSellingModelId;
	global CPQProductSellingModelOptionOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}