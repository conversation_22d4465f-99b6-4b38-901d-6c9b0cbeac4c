global class ContractDocumentVersionReviewersListOutputRepresentation {
	global String contractDocumentVersionId;
	global String persona;
	global List<ConnectApi.ContractDocumentVersionReviewerOutputRepresentation> reviewersData;
	global ContractDocumentVersionReviewersListOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}