global class CouponCodeRedemptionResult {
	global Integer availableRedemptions;
	global Integer availableRedemptionsThisBuyer;
	global String couponCode;
	global String errorMsg;
	global Boolean isSuccess;
	global Integer redemptionLimit;
	global Integer redemptionLimitPerBuyer;
	global CouponCodeRedemptionResult() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}