global class DatacloudCompany {
	global Integer activeContacts;
	global ConnectApi.Address address;
	global Double annualRevenue;
	global String companyId;
	global String description;
	global String dunsNumber;
	global String industry;
	global Boolean isInactive;
	global Boolean isOwned;
	global String naicsCode;
	global String naicsDescription;
	global String name;
	global Integer numberOfEmployees;
	global String ownership;
	global List<ConnectApi.PhoneNumber> phoneNumbers;
	global String sic;
	global String sicDescription;
	global String site;
	global String tickerSymbol;
	global String tradeStyle;
	global Datetime updatedDate;
	global String website;
	global String yearStarted;
	global DatacloudCompany() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}