global class CPQProductSellingModelOutputRepresentation {
	global Boolean doesAutoRenewByDefault;
	global String id;
	global String name;
	global Integer pricingTerm;
	global String pricingTermUnit;
	global String sellingModelType;
	global String status;
	global CPQProductSellingModelOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}