global class DataSpaceFilterConditionRepresentation {
	global String fieldName;
	global String filterValue;
	global ConnectApi.DataSpaceFilterConditionOperator operator;
	global String tableName;
	global DataSpaceFilterConditionRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}