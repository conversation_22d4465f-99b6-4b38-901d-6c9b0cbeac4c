global class CPQProductAttributeOutputRepresentation {
	global Map<String,Object> additionalFields;
	global String attributeCategoryId;
	global String attributeNameOverride;
	global ConnectApi.CPQAttributePickListOutputRepresentation attributePickList;
	global String code;
	global String dataType;
	global String defaultHelpText;
	global String defaultValue;
	global String description;
	global String developerName;
	global String displayTypeOverride;
	global Boolean hidden;
	global String id;
	global Boolean isCloneable;
	global Boolean isConfigurable;
	global Boolean isEncrypted;
	global Boolean isPriceImpacting;
	global Boolean isReadOnly;
	global Boolean isRequired;
	global String label;
	global String maximumValue;
	global String minimumValue;
	global String name;
	global Integer sequence;
	global String status;
	global String stepValue;
	global ConnectApi.UnitOfMeasureOutputRepresentation unitOfMeasure;
	global String userValue;
	global String valueDecoder;
	global String valueDescription;
	global CPQProductAttributeOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}