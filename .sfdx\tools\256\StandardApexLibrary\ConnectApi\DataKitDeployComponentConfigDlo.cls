global class DataKitDeployComponentConfigDlo {
	global String apiName;
	global String dataSourceObjectDevName;
	global List<ConnectApi.DataSpaceFilterConditionApiConfig> dataSpaceFilterCriteriaApiConfig;
	global String dataSpaceName;
	global String label;
	global DataKitDeployComponentConfigDlo() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}