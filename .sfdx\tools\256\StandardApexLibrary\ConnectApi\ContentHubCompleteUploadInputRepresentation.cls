global class ContentHubCompleteUploadInputRepresentation {
	global String contentType;
	global String contextId;
	global String fileId;
	global String fileName;
	global String uploadId;
	global List<ConnectApi.ContentHubUploadPartsInputRepresentation> uploadParts;
	global ContentHubCompleteUploadInputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}