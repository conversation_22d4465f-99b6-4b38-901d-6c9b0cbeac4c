global class CorporateMemberOutput {
	global String accountId;
	global String loyaltyProgramMemberId;
	global String loyaltyProgramName;
	global String membershipNumber;
	global List<ConnectApi.TransactionJournalOutput> transactionJournals;
	global CorporateMemberOutput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}