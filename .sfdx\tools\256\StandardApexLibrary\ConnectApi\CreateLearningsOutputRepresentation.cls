global class CreateLearningsOutputRepresentation {
	global List<ConnectApi.CreateLearningOutputRepresentation> details;
	global Integer failureCount;
	global String status;
	global Integer successCount;
	global CreateLearningsOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}