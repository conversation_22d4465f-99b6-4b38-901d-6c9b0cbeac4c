global class DataGraphField {
	global ConnectApi.DaoObjectFieldTypeQueryEnum ciFieldType;
	global String dataGraphFieldDevName;
	global String dataType;
	global String developerName;
	global String isProjected;
	global String keyCol;
	global String keyQualifierName;
	global String length;
	global String lookupCol;
	global String usageTag;
	global DataGraphField() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}