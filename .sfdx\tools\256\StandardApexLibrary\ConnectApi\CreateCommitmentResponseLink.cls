global class CreateCommitmentResponseLink {
	global ConnectApi.LinkDetails account;
	global ConnectApi.LinkDetails giftcommitment;
	global ConnectApi.LinkDetails giftcommitmentschedule;
	global List<ConnectApi.LinkDetails> giftdefaultdesignation;
	global ConnectApi.LinkDetails gifttransaction;
	global ConnectApi.LinkDetails paymentinstrument;
	global CreateCommitmentResponseLink() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}