global class ConversationResponse {
	global String generationsId;
	global String knowledgeArticleVersionId;
	global String recommendationId;
	global Double relevanceScore;
	global String relevantChunk;
	global String relevantSnippet;
	global ConversationResponse() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}