global class CreateLineItemMapsInputRepresentation {
	global Map<String,ConnectApi.CreateLineItemMapObjectInputRepresentation> adQuoteLineFieldValues;
	global Map<String,ConnectApi.CreateLineItemMapObjectInputRepresentation> quoteLineItemFieldValues;
	global Map<String,ConnectApi.CreateLineItemMapObjectInputRepresentation> quoteLineRelationshipFieldValues;
	global CreateLineItemMapsInputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}