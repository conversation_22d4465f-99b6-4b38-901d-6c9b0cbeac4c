global class CreateGiftResponseLink {
	global ConnectApi.LinkDetails account;
	global ConnectApi.LinkDetails giftcommitment;
	global ConnectApi.LinkDetails gifttransaction;
	global List<ConnectApi.LinkDetails> gifttransactiondesignation;
	global ConnectApi.LinkDetails paymentinstrument;
	global CreateGiftResponseLink() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}