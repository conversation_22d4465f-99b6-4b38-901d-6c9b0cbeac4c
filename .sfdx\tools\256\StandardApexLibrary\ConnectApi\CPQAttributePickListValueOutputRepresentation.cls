global class CPQAttributePickListValueOutputRepresentation {
	global String code;
	global String description;
	global String displayValue;
	global String id;
	global Boolean isBooleanValue;
	global String label;
	global String name;
	global Integer sequence;
	global String status;
	global String textValue;
	global CPQAttributePickListValueOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}