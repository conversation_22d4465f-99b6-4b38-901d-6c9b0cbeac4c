global class CPQSearchProductsOutputRepresentation {
	global List<ConnectApi.CPQCategoryOutputRepresentation> categories;
	global List<ConnectApi.CPQProductPricesOutputRepresentation> prices;
	global CPQSearchProductsOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}