global class ContextualDataInput {
	global String clientSession;
	global String entity;
	global String entityType;
	global Double loggedAt;
	global String loggerAppName;
	global String loggerName;
	global String pageContext;
	global String pageUrl;
	global ContextualDataInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}