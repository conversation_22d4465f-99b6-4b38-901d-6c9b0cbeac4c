global class CreateCommitmentOutputRepresentation {
	global List<ConnectApi.CreateCommitmentResponseDetails> details;
	global Integer failures;
	global Integer notProcessed;
	global Integer successes;
	global CreateCommitmentOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}