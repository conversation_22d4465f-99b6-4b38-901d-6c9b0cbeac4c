global enum DataKitComponentTypeEnum {
ACTIVATIONTARGET,
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>IGHT,
<PERSON>AT<PERSON><PERSON><PERSON>,
<PERSON>AT<PERSON><PERSON><PERSON><PERSON>RGET,
DAT<PERSON><PERSON>NECTION,
DATAGRAPH,
DATALAKEOBJECT,
DATAMODELOBJECT,
DAT<PERSON>EM<PERSON><PERSON>CSEARCH,
<PERSON><PERSON><PERSON><PERSON><PERSON>,
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
DATATRA<PERSON>FOR<PERSON>,
<PERSON>NG<PERSON>EM<PERSON><PERSON>IGNA<PERSON>,
IDEN<PERSON><PERSON><PERSON>SOLUTION,
IRRE<PERSON><PERSON><PERSON><PERSON>IS<PERSON>NRICHMENT,
MARKE<PERSON><PERSON>MENT,
MARKETSEGMENTACTIVATION,
<PERSON><PERSON><PERSON><PERSON><PERSON>UREDMODEL,
MLPREDICTIONJOB,
MLRETRIEVER,
PERSONALIZATIONOBJECTIVE,
PERSONAL<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
PERSONA<PERSON><PERSON>ZATIONSCHEMA,
SEMANTICMODEL,
TU<PERSON><PERSON>PLATEDOBJECT
}