global class DataKitDeployBundleConfigStreamingApp {
	global String connectorName;
	global List<ConnectApi.DataSpaceFilterConditionApiConfig> dataSpaceFilterCriteriaApiConfig;
	global ConnectApi.StreamingAppConnectorTypeEnum streamingAppDataConnectorType;
	global DataKitDeployBundleConfigStreamingApp() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}