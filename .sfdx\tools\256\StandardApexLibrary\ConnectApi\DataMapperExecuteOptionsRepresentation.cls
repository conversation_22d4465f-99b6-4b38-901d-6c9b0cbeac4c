global class DataMapperExecuteOptionsRepresentation {
	global Boolean ignoreCache;
	global Boolean ignoreMetadataCache;
	global Boolean ignoreMetadataPermissions;
	global String locale;
	global Boolean resetCache;
	global Boolean shouldIgnoreCommit;
	global Boolean shouldSendLegacyResponse;
	global Boolean withoutSharing;
	global DataMapperExecuteOptionsRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}