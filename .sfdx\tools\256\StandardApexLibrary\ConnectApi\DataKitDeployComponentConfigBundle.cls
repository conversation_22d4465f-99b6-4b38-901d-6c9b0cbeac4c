global class DataKitDeployComponentConfigBundle {
	global ConnectApi.DataKitDeployBundleConfig bundleConfig;
	global String bundleName;
	global ConnectApi.DataKitBundleConnectorTypeEnum connectorType;
	global Boolean forceNoRefresh;
	global DataKitDeployComponentConfigBundle() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}