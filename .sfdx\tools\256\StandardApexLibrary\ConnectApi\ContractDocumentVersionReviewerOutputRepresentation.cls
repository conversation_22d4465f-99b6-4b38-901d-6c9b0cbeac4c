global class ContractDocumentVersionReviewerOutputRepresentation {
	global String accessType;
	global String contractDocumentReviewRecordId;
	global String contractDocumentReviewStatus;
	global String displayName;
	global String email;
	global ContractDocumentVersionReviewerOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}