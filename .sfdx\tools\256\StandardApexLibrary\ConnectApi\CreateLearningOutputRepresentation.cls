global class CreateLearningOutputRepresentation {
	global List<ConnectApi.EducationApiErrorDetails> errors;
	global List<String> learningAchievementIds;
	global String learningCourseId;
	global List<String> learningFoundationItemIds;
	global String learningId;
	global List<String> learningOutcomeItemIds;
	global String learningProgramId;
	global Boolean success;
	global CreateLearningOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}