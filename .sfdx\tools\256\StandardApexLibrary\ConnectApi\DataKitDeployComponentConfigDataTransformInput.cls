global class DataKitDeployComponentConfigDataTransformInput {
	global String apiName;
	global Map<String,String> componentProperties;
	global String dataSpaceName;
	global String dataTransformDevName;
	global String dataTransformType;
	global String label;
	global DataKitDeployComponentConfigDataTransformInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}