global class DataSpaceMemberPutCollectionRepresentation {
	global ConnectApi.DataSpaceMemberCollectionRepresentation dataSpaceMembers;
	global List<ConnectApi.DataSpaceMemberErrorRepresentation> errors;
	global String success;
	global DataSpaceMemberPutCollectionRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}