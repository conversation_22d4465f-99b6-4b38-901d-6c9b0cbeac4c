global class ConversationApplicationDefinitionDetailRespresentation {
	global ConnectApi.BotInfoRepresentation botInfo;
	global String errorMessage;
	global ConnectApi.ConversationApplicationIntegrationType integrationApplication;
	global String integrationName;
	global Boolean isSuccess;
	global String runtimeUrl;
	global ConversationApplicationDefinitionDetailRespresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}