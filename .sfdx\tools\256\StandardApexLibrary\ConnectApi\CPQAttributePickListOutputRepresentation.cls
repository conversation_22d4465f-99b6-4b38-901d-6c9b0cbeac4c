global class CPQAttributePickListOutputRepresentation {
	global String dataType;
	global String description;
	global String id;
	global String name;
	global String status;
	global List<ConnectApi.CPQAttributePickListValueOutputRepresentation> values;
	global CPQAttributePickListOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}