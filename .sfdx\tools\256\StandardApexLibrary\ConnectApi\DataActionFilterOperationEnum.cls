global enum DataActionFilterOperationEnum {
BEGIN_WITH,
CH<PERSON><PERSON><PERSON>_FROM,
CHANGED_TO,
CONTAINS,
CONTAINS_REGEX,
END_WITH,
EQUAL,
EXIST_AS_WHOLE_WORD,
G<PERSON><PERSON><PERSON>TH<PERSON>,
G<PERSON>AT<PERSON><PERSON><PERSON>OREQUALTO,
HAS_DECREASED,
HAS_INCREASED,
IN_OPERATOR,
IS_CHANGED,
IS_NULL,
LESSTHAN,
LESSTHANOREQUALTO,
NO_VALUE,
NOT_CONTAINS,
NOT_CONTAINS_REGEX,
NOT_EQUAL,
NOT_IN
}