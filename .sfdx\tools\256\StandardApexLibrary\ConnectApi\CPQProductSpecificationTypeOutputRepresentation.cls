global class CPQProductSpecificationTypeOutputRepresentation {
	global String name;
	global ConnectApi.CPQProductSpecificationRecordTypeOutputRepresentation productSpecificationRecordType;
	global CPQProductSpecificationTypeOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}