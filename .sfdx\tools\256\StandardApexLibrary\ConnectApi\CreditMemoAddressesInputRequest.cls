global class CreditMemoAddressesInputRequest {
	global ConnectApi.RevenueAddressInputRepresentation billingAddress;
	global ConnectApi.RevenueAddressInputRepresentation shipFromAddress;
	global ConnectApi.RevenueAddressInputRepresentation shippingAddress;
	global CreditMemoAddressesInputRequest() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}