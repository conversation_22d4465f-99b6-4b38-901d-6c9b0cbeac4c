global class CPQSearchProductsFacetRepresentation {
	global String attributeType;
	global String displayName;
	global Integer displayRank;
	global String nameOrId;
	global List<ConnectApi.CPQFacetValueRepresentation> values;
	global CPQSearchProductsFacetRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}