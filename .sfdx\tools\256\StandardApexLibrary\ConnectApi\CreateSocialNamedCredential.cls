global class CreateSocialNamedCredential {
	global String authUrl;
	global String externalCredentialDeveloperName;
	global String namedCredentialDeveloperName;
	global ConnectApi.SocialStatusRepresentation status;
	global CreateSocialNamedCredential() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}