global class CPQCatalogOutputRepresentation {
	global String catalogCode;
	global String catalogType;
	global Map<String,Object> customFields;
	global String description;
	global String effectiveEndDate;
	global String effectiveStartDate;
	global String id;
	global String name;
	global Integer numberOfCategories;
	global String status;
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}