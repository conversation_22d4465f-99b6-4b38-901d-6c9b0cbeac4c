global class CreateLineItemConfigurationOptionsInputRepresentation {
	global Boolean addDefaultConfiguration;
	global Boolean executeConfigurationRules;
	global Boolean validateAmendRenewCancel;
	global Boolean validateProductCatalog;
	global CreateLineItemConfigurationOptionsInputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}