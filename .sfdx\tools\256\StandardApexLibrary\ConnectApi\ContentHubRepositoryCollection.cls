global class ContentHubRepositoryCollection {
	global String currentPageUrl;
	global String nextPageUrl;
	global String previousPageUrl;
	global List<ConnectApi.ContentHubRepository> repositories;
	global ContentHubRepositoryCollection() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}