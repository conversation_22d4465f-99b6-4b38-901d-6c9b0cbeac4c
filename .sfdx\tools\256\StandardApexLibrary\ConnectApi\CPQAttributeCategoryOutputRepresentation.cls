global class CPQAttributeCategoryOutputRepresentation {
	global String code;
	global String description;
	global String id;
	global String name;
	global List<ConnectApi.CPQProductAttributeOutputRepresentation> records;
	global String status;
	global Integer totalSize;
	global String usageType;
	global CPQAttributeCategoryOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}