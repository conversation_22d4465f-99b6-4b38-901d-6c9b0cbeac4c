global class CPQProductDetailsOutputRepresentation {
	global List<ConnectApi.CPQProductAttributeOutputRepresentation> attributes;
	global List<ConnectApi.CPQProductDetailsOutputRepresentation> childProducts;
	global List<ConnectApi.CPQProductPricesOutputRepresentation> prices;
	global CPQProductDetailsOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}