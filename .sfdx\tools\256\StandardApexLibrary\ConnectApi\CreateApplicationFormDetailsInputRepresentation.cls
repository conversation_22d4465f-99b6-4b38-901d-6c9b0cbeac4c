global class CreateApplicationFormDetailsInputRepresentation {
	global ConnectApi.applicationFormAccountInput account;
	global List<ConnectApi.applicantWrapperInput> applicants;
	global ConnectApi.applicationFormInput applicationForm;
	global List<ConnectApi.applicationFormProductInput> applicationFormProducts;
	global CreateApplicationFormDetailsInputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}