global class ContentHubRepository {
	global ConnectApi.ContentHubRepositoryAuthentication authentication;
	global ConnectApi.ContentHubRepositoryFeatures features;
	global String label;
	global ConnectApi.ContentHubProviderType providerType;
	global String rootFolderItemsUrl;
	global ContentHubRepository() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}