global class DataProviderNode {
	global String defaultExpressionKey;
	global String fullyQualifiedName;
	global String inputSchemaUrl;
	global String instanceURL;
	global String namespace;
	global String outputSchemaUrl;
	global DataProviderNode() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}