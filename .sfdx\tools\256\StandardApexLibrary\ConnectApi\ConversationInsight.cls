global class ConversationInsight {
	global String category;
	global String generatedText;
	global String insightTypeId;
	global String keyword;
	global List<ConnectApi.ConversationInsightTimes> momentTimes;
	global ConversationInsight() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}